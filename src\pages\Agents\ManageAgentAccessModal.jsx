import React, { useState, useEffect } from 'react';
import { useMutation } from '@apollo/client';
import toast from 'react-hot-toast';
import { SET_AGENT_ROLE } from '../../../api/mutations';
import styles from '../../../css/modal.module.css';

const AGENT_ROLES = ['USER', 'ADMIN', 'DEVELOPER'];


const ManageAgentAccessForm = ({ onSubmit, onCancel, currentRole, agentUsername }) => {
  const [role, setRole] = useState(currentRole || 'USER');
  const [errorMessage, setErrorMessage] = useState('');

  useEffect(() => {
    setRole(currentRole || 'USER');
  }, [currentRole]);

  const handleInternalSubmit = (e) => {
    e.preventDefault();
    setErrorMessage('');

    if (!role.trim()) {
      setErrorMessage('Role is required.');
      return;
    }

    onSubmit({ role: role.trim() });
  };

  return (
    <form onSubmit={handleInternalSubmit}>
      <div className={styles.modalHeader}>
        <h4>Manage Access for {agentUsername}</h4>
      </div>
      <div className={styles.modalBody}>
        {errorMessage && <p className={styles.errorMessage}>{errorMessage}</p>}
        <div className={styles.formGroup}>
          <label htmlFor="agentRole">Role:</label>
          <select
            id="agentRole"
            value={role}
            onChange={(e) => setRole(e.target.value)}
            className={styles.darkInput}
            required
          >
            {AGENT_ROLES.map(r => (
              <option key={r} value={r}>{r}</option>
            ))}
          </select>
        </div>

      </div>
      <div className={styles.modalFooter}>
        <button type="button" className={styles.secondary} onClick={onCancel}>
          Cancel
        </button>
        <button type="submit" className={styles.primary}>
          Update Access
        </button>
      </div>
    </form>
  );
};

const ManageAgentAccessModal = ({ isOpen, onClose, agent, onRefetch }) => {
  const [setAgentRole, { loading }] = useMutation(SET_AGENT_ROLE);

  if (!isOpen || !agent) return null;

  const handleFormSubmitRelay = async (formData) => {
    try {
      await setAgentRole({
        variables: {
          agentHiveId: agent.hiveId,
          role: formData.role
        }
      });

      onRefetch();
      onClose();
      toast.success('Agent role updated successfully!');
    } catch (error) {
      toast.error(error.message || 'Could not update agent role. Please try again.');
    }
  };

  const handleClose = () => {
    if (!loading) {
      onClose();
    }
  };

  return (
    <div className={styles.modalBackdrop} onClick={handleClose}>
      <div className={styles.modalContent} onClick={(e) => e.stopPropagation()}>
        <button className={styles.modalCloseButton} onClick={handleClose} disabled={loading}>
          &times;
        </button>
        
        <ManageAgentAccessForm
          onSubmit={handleFormSubmitRelay}
          onCancel={handleClose}
          currentRole={agent.role}
          agentUsername={agent.username}
        />
      </div>
    </div>
  );
};

export default ManageAgentAccessModal;
