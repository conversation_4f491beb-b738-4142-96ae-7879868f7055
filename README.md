# Hive GraphQL Server

This project exposes a GraphQL API backed by Neo4j. It manages entities such as agents, cases, operations and tasks. The server is built with Node.js, Express, Apollo Server and the `@neo4j/graphql` library.

## Prerequisites

- Node.js 20 or later
- A running Neo4j instance

## Environment Variables

Create a `.env` file in the project root containing the following variables:

- `NEO4J_URI` – Neo4j bolt connection string (e.g. `bolt://localhost:7687`)
- `NEO4J_DATABASE` – Neo4j database name
- `NEO4J_USER` – Neo4j username
- `NEO4J_PASSWORD` – Neo4j password
- `PORT` – port number for the API server
- `CORS_ORIGINS` – comma separated list of allowed CORS origins
- `JWT_SECRET` – secret used to sign authentication tokens
- `NODE_ENV` – (optional) application environment

## Install Dependencies

```bash
npm install
```

## Run the Server

```bash
npm start
```

The server will start and listen for GraphQL requests at `http://localhost:$PORT/graphql`.

## Running Tests

This project uses [Jest](https://jestjs.io) for unit testing. Install
dependencies with `npm install` and then run:

```bash
npm test
```

## Generating Tokens

To generate authentication tokens for agents, use the `generate-token` script:

```bash
npm run generate-token <username>
```

This will output a JWT token that can be used in the `Authorization` header of
API requests.


