# Keystone

## Setup

```bash
npm install
npm run dev
```

The dev server settings are defined in `vite.config.js`. The GraphQL endpoint defaults to `http://localhost:4000/graphql` in `api/apolloClient.js`.

## Project structure

- `src/` – React components, pages, hooks and utilities.
- `api/` – Apollo client configuration with GraphQL queries and mutations.
- `css/` – App styles.
- `index.html` – Entry HTML file.

